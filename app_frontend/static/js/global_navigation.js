/**
 * Global Navigation System v3.0
 * Handles all navigation with automatic authentication checks
 * 
 * Features:
 * - Automatic authentication checks before navigation
 * - Smart return URL management
 * - Universal button/link handling
 * - Login flow management
 */

class GlobalNavigationSystem {
    constructor() {
        // Get URLs from Django settings
        this.API_BASE_URL = window.API_BASE_URL || 'http://192.168.50.180:8001';
        this.LOCAL_BASE_URL = window.LOCAL_BASE_URL || 'http://192.168.50.180:8000';
        
        console.log('🌐 Global Navigation System v3.0 initializing...');
        console.log('📍 API Base URL:', this.API_BASE_URL);
        console.log('📍 Local Base URL:', this.LOCAL_BASE_URL);
        
        this.init();
    }

    /**
     * Initialize the navigation system
     */
    init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setupEventListeners());
        } else {
            this.setupEventListeners();
        }
    }

    /**
     * Set up global event listeners for navigation
     */
    setupEventListeners() {
        console.log('🔧 Setting up global navigation event listeners...');
        
        // Handle all clicks on the document
        document.addEventListener('click', (event) => {
            this.handleGlobalClick(event);
        });
        
        console.log('✅ Global navigation event listeners ready');
    }

    /**
     * Handle all clicks globally
     */
    handleGlobalClick(event) {
        const element = event.target.closest('a, button');
        if (!element) return;

        // Check for navigation attributes
        const navUrl = element.getAttribute('data-nav-url') || element.getAttribute('href');
        const authRequired = element.getAttribute('data-auth-required') === 'true';
        const isLoginLink = element.getAttribute('data-login-link') === 'true';
        
        // Handle login links
        if (isLoginLink || (navUrl && navUrl.includes('/login'))) {
            event.preventDefault();
            this.goToLogin();
            return;
        }
        
        // Handle navigation with auth check
        if (navUrl && (authRequired || this.requiresAuth(navUrl))) {
            event.preventDefault();
            this.navigateTo(navUrl, true);
            return;
        }
        
        // Handle regular navigation
        if (navUrl && element.getAttribute('data-nav-url')) {
            event.preventDefault();
            this.navigateTo(navUrl, false);
            return;
        }
    }

    /**
     * Check if a URL requires authentication
     */
    requiresAuth(url) {
        const protectedPaths = [
            '/profile/',
            '/settings/',
            '/audio_upload/',
            '/audio-history/',
            '/dashboard/',
            '/notifications/',
            '/user/',
            '/audio-detail/'
        ];
        
        return protectedPaths.some(path => url.includes(path));
    }

    /**
     * Navigate to a URL with optional authentication check
     */
    navigateTo(url, requiresAuth = false) {
        console.log(`🧭 Navigating to: ${url} (Auth required: ${requiresAuth})`);
        
        if (requiresAuth && !this.isAuthenticated()) {
            console.log('🔐 Authentication required, redirecting to login first');
            this.saveReturnUrl(url);
            this.goToLogin();
            return;
        }
        
        console.log('✅ Navigation allowed, redirecting...');
        window.location.href = url;
    }

    /**
     * Go to login page
     */
    goToLogin() {
        console.log('🔑 Redirecting to login page...');
        
        // Save current page as return URL if not already set
        if (!sessionStorage.getItem('navigation_return_url')) {
            this.saveReturnUrl(window.location.href);
        }
        
        window.location.href = '/login/';
    }

    /**
     * Handle successful login
     */
    handleLoginSuccess() {
        console.log('🎉 Login successful, handling redirect...');
        
        const returnUrl = this.getReturnUrl();
        
        if (returnUrl && returnUrl !== '/login/' && !returnUrl.includes('/login')) {
            console.log('📍 Redirecting to saved URL:', returnUrl);
            this.clearReturnUrl();
            window.location.href = returnUrl;
        } else {
            console.log('🏠 No return URL, redirecting to home');
            window.location.href = '/';
        }
    }

    /**
     * Check if user is authenticated
     */
    isAuthenticated() {
        const token = localStorage.getItem('access_token');
        if (!token) return false;
        
        try {
            // Basic JWT token validation
            const payload = JSON.parse(atob(token.split('.')[1]));
            const now = Date.now() / 1000;
            return payload.exp > now;
        } catch (error) {
            console.warn('🚫 Invalid token format');
            return false;
        }
    }

    /**
     * Save return URL for after login
     */
    saveReturnUrl(url) {
        // Clean the URL
        const cleanUrl = url.replace(window.location.origin, '');
        console.log('💾 Saving return URL:', cleanUrl);
        sessionStorage.setItem('navigation_return_url', cleanUrl);
    }

    /**
     * Get saved return URL
     */
    getReturnUrl() {
        return sessionStorage.getItem('navigation_return_url');
    }

    /**
     * Clear saved return URL
     */
    clearReturnUrl() {
        sessionStorage.removeItem('navigation_return_url');
        sessionStorage.removeItem('redirectAfterLogin');
        sessionStorage.removeItem('originalPageBeforeLogin');
    }
}

// Initialize global navigation system
const globalNav = new GlobalNavigationSystem();

// Export global functions for backward compatibility
window.globalNav = globalNav;
window.navigateTo = (url, requiresAuth = false) => globalNav.navigateTo(url, requiresAuth);
window.goToLogin = () => globalNav.goToLogin();
window.handleLoginSuccess = () => globalNav.handleLoginSuccess();

console.log('🌐 Global Navigation System v3.0 loaded successfully');
