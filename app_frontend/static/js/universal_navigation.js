/**
 * Universal Navigation System v4.0
 * Handles ALL navigation with automatic authentication checks
 * 
 * Features:
 * - Universal authentication checks before any navigation
 * - Smart return URL management for login flow
 * - Handles all buttons and links globally
 * - Login success redirect management
 */

class UniversalNavigationSystem {
    constructor() {
        // Get API configuration from Django settings
        this.API_BASE_URL = window.API_BASE_URL || 'http://192.168.50.180:8001';
        this.LOCAL_BASE_URL = window.LOCAL_BASE_URL || 'http://192.168.50.180:8000';
        
        console.log('🌐 Universal Navigation System v4.0 initializing...');
        console.log('📡 API Base URL:', this.API_BASE_URL);
        console.log('🏠 Local Base URL:', this.LOCAL_BASE_URL);
        
        this.init();
    }

    /**
     * Initialize the navigation system
     */
    init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setupEventListeners());
        } else {
            this.setupEventListeners();
        }
    }

    /**
     * Set up global event listeners for navigation
     */
    setupEventListeners() {
        console.log('🔧 Setting up universal navigation event listeners...');
        
        // Handle all clicks on the document - this is the main navigation handler
        document.addEventListener('click', (event) => {
            this.handleGlobalClick(event);
        });
        
        console.log('✅ Universal navigation event listeners ready');
    }

    /**
     * Handle all clicks globally - this is the main navigation handler
     */
    handleGlobalClick(event) {
        const element = event.target.closest('a, button');
        if (!element) return;

        // Get navigation URL from various attributes
        const navUrl = element.getAttribute('data-nav-url') || 
                      element.getAttribute('href') ||
                      element.getAttribute('onclick')?.match(/(?:window\.location\.href|location\.href)\s*=\s*['"`]([^'"`]+)['"`]/)?.[1];
        
        // Skip if no navigation URL found or it's a special URL
        if (!navUrl || navUrl === '#' || navUrl.startsWith('javascript:') || 
            navUrl.startsWith('mailto:') || navUrl.startsWith('tel:')) {
            return;
        }

        // Check if this is a login button/link
        const isLoginButton = element.id === 'global-signin-btn' || 
                             element.classList.contains('signin') ||
                             element.getAttribute('data-login-link') === 'true' ||
                             navUrl.includes('/login');

        // Handle login buttons - always go to login and return to current page
        if (isLoginButton) {
            event.preventDefault();
            this.goToLogin();
            return;
        }

        // Check if navigation requires authentication
        const requiresAuth = this.requiresAuth(navUrl);
        
        if (requiresAuth) {
            event.preventDefault();
            this.navigateWithAuth(navUrl);
        }
        // For non-protected URLs, let the browser handle normally
    }

    /**
     * Check if a URL requires authentication
     */
    requiresAuth(url) {
        const protectedPaths = [
            '/user/profile/',
            '/user/settings/',
            '/audio_upload/',
            '/audio-history/',
            '/dashboard/',
            '/notifications/',
            '/audio-detail/',
            '/message/'
        ];
        
        return protectedPaths.some(path => url.includes(path));
    }

    /**
     * Navigate to a URL that requires authentication
     */
    navigateWithAuth(url) {
        console.log(`🔐 Checking auth for: ${url}`);
        
        if (this.isAuthenticated()) {
            console.log('✅ User authenticated, navigating to:', url);
            window.location.href = url;
        } else {
            console.log('🔑 Authentication required, saving URL and redirecting to login');
            this.saveReturnUrl(url);
            this.goToLogin();
        }
    }

    /**
     * Go to login page
     */
    goToLogin() {
        console.log('🔑 Redirecting to login page...');
        
        // Save current page as return URL if not already set and not login/register page
        const currentUrl = window.location.href;
        if (!this.getReturnUrl() && !currentUrl.includes('/login') && !currentUrl.includes('/register')) {
            this.saveReturnUrl(currentUrl);
        }
        
        window.location.href = '/login/';
    }

    /**
     * Handle successful login - redirect to intended page
     */
    handleLoginSuccess() {
        console.log('🎉 Login successful, handling redirect...');
        
        const returnUrl = this.getReturnUrl();
        
        if (returnUrl && !returnUrl.includes('/login') && !returnUrl.includes('/register')) {
            console.log('📍 Redirecting to saved URL:', returnUrl);
            this.clearReturnUrl();
            window.location.replace(returnUrl);
        } else {
            console.log('🏠 No return URL, redirecting to home');
            window.location.replace('/');
        }
    }

    /**
     * Check if user is authenticated
     */
    isAuthenticated() {
        const token = localStorage.getItem('access_token');
        if (!token) return false;
        
        try {
            // Basic token validation (check if not expired)
            const payload = JSON.parse(atob(token.split('.')[1]));
            const now = Date.now() / 1000;
            return payload.exp > now;
        } catch (error) {
            console.warn('🔍 Token validation failed:', error);
            return false;
        }
    }

    /**
     * Save return URL for after login
     */
    saveReturnUrl(url) {
        // Don't save login or register pages as return URLs
        if (url.includes('/login') || url.includes('/register')) {
            return;
        }
        
        sessionStorage.setItem('navigation_return_url', url);
        console.log('💾 Saved return URL:', url);
    }

    /**
     * Get saved return URL
     */
    getReturnUrl() {
        return sessionStorage.getItem('navigation_return_url') || 
               sessionStorage.getItem('redirectAfterLogin');
    }

    /**
     * Clear saved return URL
     */
    clearReturnUrl() {
        sessionStorage.removeItem('navigation_return_url');
        sessionStorage.removeItem('redirectAfterLogin');
        sessionStorage.removeItem('originalPageBeforeLogin');
    }

    /**
     * Public method to navigate to any URL with auth check
     */
    navigateTo(url, forceAuthCheck = false) {
        if (forceAuthCheck || this.requiresAuth(url)) {
            this.navigateWithAuth(url);
        } else {
            window.location.href = url;
        }
    }
}

// Initialize universal navigation system
const universalNav = new UniversalNavigationSystem();

// Export global functions for backward compatibility and easy access
window.universalNav = universalNav;
window.globalNav = universalNav; // Keep for backward compatibility
window.navigateTo = (url, requiresAuth = false) => universalNav.navigateTo(url, requiresAuth);
window.goToLogin = () => universalNav.goToLogin();
window.handleLoginSuccess = () => universalNav.handleLoginSuccess();

console.log('🌐 Universal Navigation System v4.0 loaded successfully');
