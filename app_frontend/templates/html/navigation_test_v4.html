{% extends 'base.html' %}
{% load static %}

{% block content %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Universal Navigation Test v4.0 - HiSage Health</title>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8fafc;
            padding: 80px 20px 20px;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .test-section {
            margin: 2rem 0;
            padding: 1.5rem;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background: #f8fafc;
        }
        
        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            margin: 0.5rem;
            border: none;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-primary { background: #3b82f6; color: white; }
        .btn-success { background: #10b981; color: white; }
        .btn-warning { background: #f59e0b; color: white; }
        .btn-secondary { background: #6b7280; color: white; }
        
        .btn:hover { transform: translateY(-1px); opacity: 0.9; }
        
        .auth-status {
            padding: 1rem;
            border-radius: 6px;
            margin-bottom: 2rem;
            font-weight: 500;
        }
        
        .auth-status.authenticated { background: #d1fae5; color: #065f46; }
        .auth-status.not-authenticated { background: #fee2e2; color: #991b1b; }
        
        #test-results {
            background: #f3f4f6;
            padding: 1rem;
            border-radius: 6px;
            font-family: monospace;
            font-size: 0.9rem;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌐 Universal Navigation System v4.0 Test</h1>
        <p>This page tests the new universal navigation system that handles ALL navigation with automatic authentication checks.</p>
        
        <div id="auth-status" class="auth-status">
            <strong>Authentication Status:</strong> <span id="auth-text">Checking...</span>
        </div>
        
        <div class="test-section">
            <h3>🔗 Login Links (Return to Current Page)</h3>
            <p>These links will take you to login and return you to this page after successful authentication.</p>
            
            <a href="/login/" class="btn btn-primary">Login Link (href)</a>
            <button class="btn btn-primary" id="global-signin-btn">Login Button (ID)</button>
            <button class="btn btn-primary" onclick="goToLogin()">Login Button (JavaScript)</button>
        </div>
        
        <div class="test-section">
            <h3>🚪 Public Navigation (No Authentication Required)</h3>
            <p>These links navigate directly without authentication checks.</p>
            
            <a href="/about/" class="btn btn-secondary">About Us</a>
            <a href="/contact-us/" class="btn btn-secondary">Contact Us</a>
            <a href="/message_board/" class="btn btn-secondary">Message Board</a>
        </div>
        
        <div class="test-section">
            <h3>🔒 Protected Navigation (Authentication Required)</h3>
            <p>These links will check authentication first. If not logged in, you'll go to login then to the target page.</p>
            
            <a href="/user/profile/" class="btn btn-success">User Profile</a>
            <a href="/audio_upload/" class="btn btn-success">Audio Upload</a>
            <a href="/audio_upload/history/" class="btn btn-success">Audio History</a>
            <a href="/dashboard/" class="btn btn-success">Dashboard</a>
            <a href="/notifications/" class="btn btn-success">Notifications</a>
        </div>
        
        <div class="test-section">
            <h3>⚡ JavaScript Function Tests</h3>
            <p>Test the JavaScript functions directly.</p>
            
            <button class="btn btn-secondary" onclick="navigateTo('/about/', false)">Navigate to About (No Auth)</button>
            <button class="btn btn-success" onclick="navigateTo('/user/profile/', true)">Navigate to Profile (Auth Required)</button>
            <button class="btn btn-warning" onclick="checkAuthStatus()">Check Auth Status</button>
            <button class="btn btn-primary" onclick="testAllFunctions()">Test All Functions</button>
        </div>
        
        <div class="test-section">
            <h3>📋 Test Results</h3>
            <div id="test-results">
                <em>Test results will appear here...</em>
            </div>
            <button class="btn btn-secondary" onclick="clearResults()">Clear Results</button>
        </div>
    </div>

    <script>
        // Test functions
        function logResult(message) {
            const results = document.getElementById('test-results');
            const timestamp = new Date().toLocaleTimeString();
            results.innerHTML += `[${timestamp}] ${message}\n`;
            results.scrollTop = results.scrollHeight;
            console.log(message);
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
        }

        function checkAuthStatus() {
            const token = localStorage.getItem('access_token');
            const authStatus = document.getElementById('auth-status');
            const authText = document.getElementById('auth-text');
            
            let isAuth = false;
            if (token) {
                try {
                    const payload = JSON.parse(atob(token.split('.')[1]));
                    const now = Date.now() / 1000;
                    isAuth = payload.exp > now;
                } catch (error) {
                    isAuth = false;
                }
            }
            
            if (isAuth) {
                authStatus.className = 'auth-status authenticated';
                authText.textContent = 'Authenticated ✅';
                logResult('✅ User is authenticated');
            } else {
                authStatus.className = 'auth-status not-authenticated';
                authText.textContent = 'Not Authenticated ❌';
                logResult('❌ User is not authenticated');
            }
            
            return isAuth;
        }

        function testAllFunctions() {
            logResult('🧪 Starting comprehensive navigation tests...');
            
            // Test universal navigation availability
            if (window.universalNav) {
                logResult('✅ Universal Navigation System v4.0 loaded successfully');
                logResult(`✅ API Base URL: ${window.universalNav.API_BASE_URL}`);
                logResult(`✅ Local Base URL: ${window.universalNav.LOCAL_BASE_URL}`);
            } else {
                logResult('❌ Universal Navigation System not found');
            }
            
            // Test global functions
            if (typeof window.navigateTo === 'function') {
                logResult('✅ window.navigateTo function available');
            } else {
                logResult('❌ window.navigateTo function missing');
            }
            
            if (typeof window.goToLogin === 'function') {
                logResult('✅ window.goToLogin function available');
            } else {
                logResult('❌ window.goToLogin function missing');
            }
            
            if (typeof window.handleLoginSuccess === 'function') {
                logResult('✅ window.handleLoginSuccess function available');
            } else {
                logResult('❌ window.handleLoginSuccess function missing');
            }
            
            // Test authentication check
            checkAuthStatus();
            
            logResult('🧪 Navigation function tests completed');
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            logResult('🧪 Universal Navigation Test v4.0 loaded');
            checkAuthStatus();
            
            // Update status every 10 seconds
            setInterval(checkAuthStatus, 10000);
            
            // Run initial tests
            setTimeout(testAllFunctions, 1000);
        });
    </script>
    
    <!-- Universal Navigation System -->
    <script src="{% static 'js/universal_navigation.js' %}"></script>
</body>
</html>
{% endblock %}
