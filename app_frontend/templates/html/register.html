{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign Up - Cognitive Health</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            position: relative;
            overflow: hidden;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
        }

        .register-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            box-shadow: 0 32px 64px rgba(0, 0, 0, 0.12), 0 0 0 1px rgba(255, 255, 255, 0.2);
            overflow: hidden;
            width: 100%;
            max-width: 440px;
            animation: slideUp 0.8s cubic-bezier(0.16, 1, 0.3, 1);
            position: relative;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(40px) scale(0.96);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .register-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 48px 32px 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .register-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .register-header h1 {
            font-size: 28px;
            margin-bottom: 8px;
            font-weight: 700;
            letter-spacing: -0.02em;
            position: relative;
            z-index: 1;
        }

        .register-header p {
            opacity: 0.9;
            font-size: 16px;
            font-weight: 400;
            position: relative;
            z-index: 1;
        }

        .register-form {
            padding: 28px 32px;
        }

        .form-group {
            margin-bottom: 18px;
            position: relative;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #374151;
            font-size: 14px;
            letter-spacing: -0.01em;
        }

        .form-group input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 400;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            background: #ffffff;
            color: #111827;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            background: #ffffff;
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
            transform: translateY(-1px);
        }

        .form-group input::placeholder {
            color: #9ca3af;
            font-weight: 400;
        }

        .register-btn {
            width: 100%;
            padding: 12px 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            margin-bottom: 18px;
            position: relative;
            overflow: hidden;
        }

        .register-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .register-btn:hover::before {
            left: 100%;
        }

        .register-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 20px 40px rgba(102, 126, 234, 0.3);
        }

        .register-btn:active {
            transform: translateY(0);
        }

        .register-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .alert {
            padding: 12px 16px;
            border-radius: 12px;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 12px;
            font-weight: 500;
            font-size: 14px;
        }

        .alert-success {
            background: #ecfdf5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }

        .alert-error {
            background: #fef2f2;
            color: #991b1b;
            border: 1px solid #fecaca;
        }

        .login-link {
            text-align: center;
            margin-top: 16px;
            padding-top: 16px;
            border-top: 1px solid #e5e7eb;
        }

        .login-link p {
            color: #6b7280;
            font-size: 14px;
        }

        .login-link a {
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
            transition: color 0.2s ease;
        }

        .login-link a:hover {
            color: #5a67d8;
            text-decoration: underline;
        }

        /* Back button removed */

        .required {
            color: #ef4444;
        }

        .name-fields {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
        }

        @media (max-width: 640px) {
            .name-fields {
                grid-template-columns: 1fr;
                gap: 12px;
            }
        }

        @media (max-width: 768px) {
            body {
                padding: 16px;
            }

            .register-container {
                border-radius: 20px;
            }

            .register-header {
                padding: 40px 24px 32px;
            }

            .register-header h1 {
                font-size: 28px;
            }

            .register-form {
                padding: 32px 24px;
            }

            /* Back button removed */
        }
    </style>
</head>
<body>
    <div class="register-container">
        <div class="register-header">
            <h1><i class="fas fa-brain"></i> HiSage </h1>
            <p>AI-Powered Dementia Screening Platform</p>
        </div>

        <div class="register-form">
            <div id="registerMessage"></div>

            <form id="registerForm">
                <div class="form-group">
                    <label for="email">Email Address <span class="required">*</span></label>
                    <input type="email" id="email" name="email" required placeholder="Enter your email address">
                </div>

                <div class="name-fields">
                    <div class="form-group">
                        <label for="first_name">First Name</label>
                        <input type="text" id="first_name" name="first_name" placeholder="First name">
                    </div>

                    <div class="form-group">
                        <label for="last_name">Surname</label>
                        <input type="text" id="last_name" name="last_name" placeholder="Surname">
                    </div>
                </div>

                <div style="margin-bottom: 14px; padding: 10px; background: #f3f4f6; border-radius: 8px; font-size: 14px; color: #6b7280;">
                    <i class="fas fa-info-circle" style="color: #3b82f6; margin-right: 8px;"></i>
                    Names are optional. If name is not provided, it will default to "User".
                </div>

                <div class="form-group">
                    <label for="password">Password <span class="required">*</span></label>
                    <input type="password" id="password" name="password" required placeholder="Enter password (minimum 8 characters)">
                </div>

                <div class="form-group">
                    <label for="password_confirm">Confirm Password <span class="required">*</span></label>
                    <input type="password" id="password_confirm" name="password_confirm" required placeholder="Re-enter your password">
                </div>

                <button type="submit" id="registerBtn" class="register-btn">
                    <i class="fas fa-user-plus"></i> Create Account
                </button>
            </form>

            <div class="login-link">
                <p>Already have an account? <a href="/login">Sign In</a></p>
            </div>
        </div>
    </div>

    <script>
        // Pass API configuration from Django to frontend
        window.API_BASE_URL = "{{ API_BASE_URL }}";
        window.LOCAL_BASE_URL = "{{ LOCAL_BASE_URL }}";
        window.API_CONFIG = {
            API_BASE_URL: '{{ API_BASE_URL }}/api',
            LOCAL_BASE_URL: '{{ LOCAL_BASE_URL }}'
        };
        // Mark this page has custom event handlers to prevent auth_api.js from duplicate binding
        window.customAuthHandlers = true;
    </script>
    <script src="{% static 'js/auth_api.js' %}?v=2.0"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔧 Register page DOMContentLoaded - customAuthHandlers:', window.customAuthHandlers);

            const registerForm = document.getElementById('registerForm');
            const registerBtn = document.getElementById('registerBtn');
            const messageDiv = document.getElementById('registerMessage');
            const authAPI = new AuthAPI();

            let isSubmitting = false; // 防重复提交标志
            let eventListenerAdded = false; // 防止重复添加事件监听器

            // 检查是否已经添加了事件监听器
            if (registerForm.dataset.listenerAdded) {
                console.log('⚠️ Event listener already added, skipping');
                return;
            }

            // 标记事件监听器已添加
            registerForm.dataset.listenerAdded = 'true';
            console.log('✅ Adding registration event listener');

            registerForm.addEventListener('submit', async function(e) {
                e.preventDefault();
                console.log('📝 Registration form submitted');

                // 强化防重复提交检查
                if (isSubmitting) {
                    console.log('⚠️ Registration already in progress, ignoring duplicate submission');
                    return;
                }

                // 立即设置提交状态并禁用表单
                isSubmitting = true;
                registerForm.style.pointerEvents = 'none';
                console.log('🔒 Form locked for submission');

                // 获取表单数据
                const formData = new FormData(registerForm);
                const userData = {
                    email: formData.get('email'),
                    password: formData.get('password'),
                    password_confirm: formData.get('password_confirm'),
                    first_name: formData.get('first_name') || '',
                    last_name: formData.get('last_name') || ''
                };

                // 验证密码
                if (userData.password !== userData.password_confirm) {
                    showMessage('Passwords do not match', 'error');
                    // 重置状态
                    isSubmitting = false;
                    registerForm.style.pointerEvents = 'auto';
                    return;
                }

                // 显示加载状态
                registerBtn.disabled = true;
                registerBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creating Account...';

                try {
                    console.log('Starting registration for:', userData.email);
                    const response = await authAPI.register(userData);
                    console.log('Registration response received:', response);

                    // 检查响应是否成功
                    if (response && response.success) {
                        console.log('Registration successful, processing success response');

                        // 立即禁用表单，防止重复提交
                        registerForm.style.pointerEvents = 'none';
                        registerBtn.disabled = true;

                        if (response.resent) {
                            // 重新发送验证邮件的情况
                            showMessage('Verification email has been resent to your email address. Please check your inbox.', 'success');
                            // 跳转到验证码页面
                            setTimeout(() => {
                                window.location.href = `/verify-code?email=${encodeURIComponent(response.email)}`;
                            }, 2000);
                        } else {
                            // 新注册成功的情况
                            showMessage('Registration successful! Verification code has been sent to your email.', 'success');
                            // 保存邮箱到localStorage，然后跳转到验证码页面
                            localStorage.setItem('pendingActivationEmail', response.email);
                            setTimeout(() => {
                                window.location.href = `/verify-code?email=${encodeURIComponent(response.email)}`;
                            }, 2000);
                        }
                        registerForm.reset();

                        // 不要在finally中重置状态，因为已经成功了
                        return;
                    } else {
                        // 注册失败，显示错误信息
                        console.log('Registration failed:', response);

                        let errorMessage = response?.message || 'Registration failed';

                        // 如果有具体的字段错误，显示它们
                        if (response?.errors) {
                            const errors = Object.values(response.errors).flat();
                            if (errors.length > 0) {
                                errorMessage = errors.join(', ');
                            }
                        }

                        showMessage(errorMessage, 'error');
                    }
                } catch (error) {
                    console.error('Registration error caught:', error);
                    console.error('Error type:', typeof error);
                    console.error('Error message:', error.message);
                    console.error('Error stack:', error.stack);

                    // 显示具体的错误消息
                    let errorMessage = 'Registration failed. Please try again.';

                    if (error.message) {
                        if (error.message.includes('重新发送验证邮件') || error.message.includes('resent')) {
                            // 这种情况应该被上面的success处理，但以防万一
                            showMessage('Verification email has been resent to your email address.', 'success');
                            return;
                        } else if (error.message.includes('邮箱已被注册') || error.message.includes('already registered') || error.message.includes('already exists')) {
                            errorMessage = 'This email is already registered. Please use a different email or try logging in.';
                        } else if (error.message.includes('未激活') || error.message.includes('not activated')) {
                            errorMessage = 'This email is registered but not activated. Please check your email or contact support.';
                        } else if (error.message.includes('24小时后重试') || error.message.includes('try again in 24 hours')) {
                            errorMessage = 'This email was recently registered. Please check your email or try again in 24 hours.';
                        } else if (error.message.includes('系统繁忙') || error.message.includes('database') || error.message.includes('locked')) {
                            errorMessage = 'System is busy, please try again in a few moments. If the problem persists, please contact support.';
                        } else if (error.message.includes('UNIQUE constraint')) {
                            errorMessage = 'This email is already registered. Please use a different email or try logging in.';
                        } else {
                            errorMessage = error.message;
                        }
                    }

                    showMessage(errorMessage, 'error');
                } finally {
                    // 只在失败时重置状态（成功时已经return了）
                    if (isSubmitting) {
                        isSubmitting = false;
                        registerForm.style.pointerEvents = 'auto';
                        registerBtn.disabled = false;
                        registerBtn.innerHTML = '<i class="fas fa-user-plus"></i> Create Account';
                    }
                }
            });

            function showMessage(message, type) {
                messageDiv.innerHTML = `<div class="alert alert-${type}">${message}</div>`;
                messageDiv.scrollIntoView({ behavior: 'smooth' });
            }

            // 处理URL参数中的邮箱
            const urlParams = new URLSearchParams(window.location.search);
            const emailParam = urlParams.get('email');
            if (emailParam) {
                document.getElementById('email').value = emailParam;
            }
        });
    </script>

    <!-- Universal Navigation System -->
    <script src="{% static 'js/universal_navigation.js' %}"></script>
</body>
</html>